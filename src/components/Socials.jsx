import { settings } from '@/lib/settings'
import React, { useState } from 'react'
import ImageWrapperResponsive from './ImageWrapperResponsive'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    
    const handleClick = () => {
      window.open(data?.url)
    }
    // console.log('BtnLandingpageComponent:',data)
    return(
        <div 
            onClick={handleClick}
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.off}/>
            </div>
        </div>
    )
}

function SoundBtnComponent({data,fn,index,onClick}) {
    const {experienceState,disptachExperience} = useContextExperience()
    const handleClick = () => {
      console.log('handleClick:',experienceState?.playAudio)
      disptachExperience({type:ACTIONS_EXPERIENCE_STATE.PLAY_AUDIO})
    }
    // console.log('BtnLandingpageComponent:',experienceState?.playAudio)
    return(
        <div 
          onClick={handleClick}
          className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
          {experienceState?.playAudio 
            ?<div 
                className={`flex top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.ov}/>
            </div>
            :<div
                className={`flex top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.off}/>
            </div>
          }
        </div>
    )
}

export default function Socials() {
  return (
    <div className='absolute right-5 bottom-20 z-20 flex-col gap-3 flex w-fit h-fit items-center justify-center'>
      {settings.socials.map((item,index)=>{
        return(
          <div key={index} className='flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'>
            <div className='flex relative items-center justify-center'>
              {item?.name=='sound' 
                ? <SoundBtnComponent 
                    index={index}
                    data={item}
                  />
                : <BtnLandingpageComponent 
                    index={index}
                    data={item}
                  />
              }
            </div>
          </div>
        )
      })}   
    </div>
  )
}
