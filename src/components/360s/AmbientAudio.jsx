'use client';

import { useRef, useEffect, useState, useCallback } from 'react';

/**
 * AmbientAudio Component
 *
 * A React Three.js Fiber component that plays ambient background music
 * using the Ambient_Music.mp4 file from the public assets folder.
 *
 * Features:
 * - Automatic playback on mount
 * - Continuous looping
 * - Configurable volume
 * - Error handling for loading failures
 * - Performance optimized with proper cleanup
 *
 * Usage:
 * <Canvas>
 *   <AmbientAudio volume={0.4} autoPlay={true} />
 * </Canvas>
 */
function AmbientAudio({
  volume = 0.4,
  autoPlay = true,
  loop = true,
  onLoad = null,
  onError = null
}) {
  const audioRef = useRef();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState(null);

  // Audio file path
  const audioUrl = '/assets/Ambient_Music.mp4';
  // const audioUrl = '/assets/audio.mp4.mp4';

  // Load and setup audio - completely non-positional approach
  useEffect(() => {
    let isMounted = true;
    let audioElement = null;

    const loadAudio = async () => {
      try {
        console.log('Loading ambient audio from:', audioUrl);

        // Create HTML5 Audio element directly (no Three.js Audio wrapper)
        audioElement = new Audio(audioUrl);
        audioElement.crossOrigin = 'anonymous';
        audioElement.loop = loop;
        audioElement.volume = volume;
        audioElement.preload = 'auto';

        // Store reference for control methods
        audioRef.current = audioElement;

        // Wait for audio to be ready
        await new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error('Audio loading timeout'));
          }, 10000);

          const handleCanPlay = () => {
            clearTimeout(timeoutId);
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            audioElement.removeEventListener('loadeddata', handleCanPlay);
            resolve();
          };

          const handleError = (err) => {
            clearTimeout(timeoutId);
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            audioElement.removeEventListener('loadeddata', handleCanPlay);
            reject(err);
          };

          // Listen for multiple ready states
          audioElement.addEventListener('canplaythrough', handleCanPlay);
          audioElement.addEventListener('loadeddata', handleCanPlay);
          audioElement.addEventListener('error', handleError);

          // Start loading
          audioElement.load();
        });

        if (isMounted) {
          setIsLoaded(true);
          setError(null);
          console.log('Ambient audio loaded successfully - ready to play');
          if (onLoad) onLoad();
        }

      } catch (err) {
        console.error('Failed to load ambient audio:', err);
        if (isMounted) {
          setError(err);
          setIsLoaded(false);
          if (onError) onError(err);
        }
      }
    };

    loadAudio();

    return () => {
      isMounted = false;
      // Cleanup HTML5 audio element
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
        audioElement.load(); // Reset the element
        audioElement = null;
      }
      audioRef.current = null;
    };
  }, [audioUrl, loop, volume, onLoad, onError]);

  // Handle autoplay and audio control with HTML5 audio
  useEffect(() => {
    if (!audioRef.current || !isLoaded) return;

    const audioElement = audioRef.current;

    // Auto-play if enabled
    if (autoPlay) {
      const playAudio = async () => {
        try {
          console.log('Attempting to play ambient audio...');
          await audioElement.play();
          setIsPlaying(true);
          console.log('Ambient audio started playing successfully');
        } catch (playError) {
          console.warn('Autoplay prevented by browser policy:', playError.message);

          // Set up a user interaction listener to start audio
          const startOnInteraction = async (event) => {
            console.log('User interaction detected, starting audio...');
            try {
              if (audioElement.paused) {
                await audioElement.play();
                setIsPlaying(true);
                console.log('Ambient audio started after user interaction');
              }
            } catch (err) {
              console.error('Failed to start audio after interaction:', err);
            }

            // Remove listeners after first successful interaction
            document.removeEventListener('click', startOnInteraction);
            document.removeEventListener('keydown', startOnInteraction);
            document.removeEventListener('touchstart', startOnInteraction);
          };

          // Add interaction listeners
          document.addEventListener('click', startOnInteraction);
          document.addEventListener('keydown', startOnInteraction);
          document.addEventListener('touchstart', startOnInteraction);

          console.log('Added user interaction listeners for audio playback');
        }
      };

      // Small delay to ensure everything is ready
      setTimeout(playAudio, 100);
    }
  }, [isLoaded, autoPlay]);

  // Update volume when prop changes
  useEffect(() => {
    if (audioRef.current && isLoaded) {
      audioRef.current.volume = volume;
      console.log('Volume updated to:', volume);
    }
  }, [volume, isLoaded]);

  // Public methods for controlling playback
  const play = useCallback(async () => {
    if (audioRef.current && isLoaded) {
      try {
        await audioRef.current.play();
        setIsPlaying(true);
        console.log('Audio play triggered manually');
      } catch (err) {
        console.error('Manual play failed:', err);
      }
    }
  }, [isLoaded]);

  const pause = useCallback(() => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      setIsPlaying(false);
      console.log('Audio paused');
    }
  }, []);

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      console.log('Audio stopped');
    }
  }, []);

  // Expose control methods for external use (optional)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.customControls = {
        play,
        pause,
        stop,
        isPlaying,
        isLoaded,
        error
      };
    }
  }, [play, pause, stop, isPlaying, isLoaded, error]);

  // Don't render anything - this is a non-visual audio component
  // The audio is managed through Three.js Audio objects in useEffect
  return null;
}

export default AmbientAudio;
