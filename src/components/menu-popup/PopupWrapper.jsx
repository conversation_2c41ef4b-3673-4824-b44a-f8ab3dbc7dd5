'use client'
import { settings } from '@/lib/settings'
import React, { useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRouter, useSearchParams } from 'next/navigation'
import MenuPopup from './MenuWrapper'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'
import VideoGalleryComponent from './VideoGalleryComponent'
import GalleryStoreComponent from './GalleryStoreComponent'
import ItemInfoComponent from './ItemInfoComponent'
import _360BookNowBtn from '../360s/_360BookNowBtn'
import { HiX } from 'react-icons/hi'
import { useAudioContextSafe } from '@/contexts/AudioContext'

function GeneralPopupWrapper({children}) {
  const {experienceState,disptachExperience,setMenuToggle}=useContextExperience()
  const { addPopup, removePopup } = useAudioContextSafe()

  // Add popup to audio context when component mounts
  useEffect(() => {
    const popupId = 'general-popup'
    addPopup(popupId)

    return () => {
      removePopup(popupId)
    }
  }, [addPopup, removePopup])

  const handlePopupClose=(e)=>{
    setMenuToggle(false)
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_TOGGLE})
  }
  // console.log('GeneralPopupWrapper:',experienceState)
  return(
    <div className='GeneralPopupWrapper flex z-20 absolute top-0 justify-center left-0 w-full h-full overflow-y-auto bg-black/75'>
      <div
        onClick={handlePopupClose}
        className=" flex absolute z-10 items-center justify-center top-0 right-0 h-[75px] w-fit text-4xl text-white cursor-pointer"
      >
        <HiX className='mr-2'/>
        <div className='w-fit h-fit invisible'>
          <_360BookNowBtn/>
        </div>
      </div>
      <div className='flex relative top-0 h-full px-5 md:w-[995px] mx-auto'>
        {children}
      </div>
    </div>
  )
}

function VideoPopupWrapper({children}) {
  const {experienceState,disptachExperience,setMenuToggle}=useContextExperience()
  const { addPopup, removePopup } = useAudioContextSafe()

  // Add popup to audio context when component mounts
  useEffect(() => {
    const popupId = 'video-popup'
    addPopup(popupId)

    return () => {
      removePopup(popupId)
    }
  }, [addPopup, removePopup])

  const handlePopupClose=(e)=>{
    setMenuToggle(false)
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_TOGGLE})
    // Remove the PLAY_AUDIO dispatch - audio resume is handled by AudioContext
  }
  // console.log('VideoPopupWrapper:',experienceState)
  return(
    <div className={`VideoPopupWrapper flex z-20 absolute top-0 justify-center left-0 w-full h-full overflow-y-auto ${!experienceState?.showVideoGallery ? 'bg-black/0' : 'bg-black/75'}`}>
      <div
        onClick={handlePopupClose}
        className=" flex absolute z-10 items-center justify-center top-0 right-0 h-[75px] w-fit text-4xl text-white cursor-pointer"
      >
        <HiX className='mr-2'/>
        <div className='w-fit h-fit invisible'>
          <_360BookNowBtn/>
        </div>
      </div>
      <div className='flex relative top-0 h-full px-5 md:w-[995px] mx-auto'>
        {children}
      </div>
    </div>
  )
}

export default function PopupWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const refGroup=useRef()
    const {experienceState,disptachExperience}=useContextExperience()
    const router=useRouter()
    const searchParams=useSearchParams()
    const query=searchParams.get('id')

    // console.log('PopupWrapper:',experienceState)

  return (
    (experienceState?.showPopup && <>
        {experienceState?.showVideoGallery || experienceState?.showSingleVideoGallery
          ? <VideoPopupWrapper>
              {experienceState?.showVideo && <VideoGalleryComponent/>}
            </VideoPopupWrapper>
          :<GeneralPopupWrapper>
            {experienceState?.showBookingPopup && <BookingFormComponent/>}
            {experienceState?.showGalleryStore && <GalleryStoreComponent/>}
            {experienceState?.showItemInfo?.showItem && <ItemInfoComponent/>}
          </GeneralPopupWrapper>
        }
    </>)
  )
}
