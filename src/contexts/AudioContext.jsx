'use client';

import { createContext, useContext, useRef, useState, useCallback, useEffect } from 'react';

const AudioContext = createContext();

/**
 * AudioContextProvider - Manages global audio state and popup interactions
 * 
 * This context provides centralized audio control for the 360-degree viewer,
 * handling pause/resume operations when popups open/close while maintaining
 * the user's audio preference settings.
 */
export function AudioContextProvider({ children }) {
  const audioRef = useRef(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true); // User preference
  const [isAudioPaused, setIsAudioPaused] = useState(false); // Popup-induced pause
  const [activePopups, setActivePopups] = useState(new Set()); // Track multiple popups
  const [audioState, setAudioState] = useState({
    isPlaying: false,
    isLoaded: false,
    currentTime: 0,
    duration: 0,
    error: null
  });

  // Register audio component reference
  const registerAudioRef = useCallback((ref) => {
    audioRef.current = ref;
    console.log('Audio component registered');
  }, []);

  // Handle user audio toggle (sound button)
  const toggleAudio = useCallback(() => {
    setIsAudioEnabled(prev => {
      const newState = !prev;
      console.log('User toggled audio:', newState ? 'enabled' : 'disabled');
      
      if (audioRef.current) {
        if (newState && activePopups.size === 0) {
          // Resume if no popups are active
          audioRef.current.resume();
        } else if (!newState) {
          // Pause if user disabled
          audioRef.current.pause();
        }
      }
      
      return newState;
    });
  }, [activePopups.size]);

  // Add popup to active set (pause audio)
  const addPopup = useCallback((popupId) => {
    setActivePopups(prev => {
      const newSet = new Set(prev);
      const wasEmpty = newSet.size === 0;
      newSet.add(popupId);
      
      console.log(`Popup opened: ${popupId}, active popups:`, Array.from(newSet));
      
      // Pause audio when first popup opens (if audio is enabled)
      if (wasEmpty && isAudioEnabled && audioRef.current) {
        audioRef.current.pause();
        setIsAudioPaused(true);
        console.log('Audio paused due to popup opening');
      }
      
      return newSet;
    });
  }, [isAudioEnabled]);

  // Remove popup from active set (resume audio if no more popups)
  const removePopup = useCallback((popupId) => {
    setActivePopups(prev => {
      const newSet = new Set(prev);
      newSet.delete(popupId);
      
      console.log(`Popup closed: ${popupId}, active popups:`, Array.from(newSet));
      
      // Resume audio when last popup closes (if audio is enabled)
      if (newSet.size === 0 && isAudioEnabled && audioRef.current) {
        audioRef.current.resume();
        setIsAudioPaused(false);
        console.log('Audio resumed after all popups closed');
      }
      
      return newSet;
    });
  }, [isAudioEnabled]);

  // Handle audio state changes from the audio component
  const handleAudioStateChange = useCallback((playing) => {
    setAudioState(prev => ({
      ...prev,
      isPlaying: playing
    }));
  }, []);

  // Update audio state when audio component reports changes
  const updateAudioState = useCallback((newState) => {
    setAudioState(prev => ({
      ...prev,
      ...newState
    }));
  }, []);

  // Force pause audio (for emergency stops)
  const forceAudioPause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      console.log('Audio force paused');
    }
  }, []);

  // Force resume audio (if conditions allow)
  const forceAudioResume = useCallback(() => {
    if (audioRef.current && isAudioEnabled && activePopups.size === 0) {
      audioRef.current.resume();
      console.log('Audio force resumed');
    }
  }, [isAudioEnabled, activePopups.size]);

  // Computed values
  const shouldAudioPlay = isAudioEnabled && activePopups.size === 0;
  const isAudioPausedByPopup = activePopups.size > 0;

  const contextValue = {
    // Audio state
    isAudioEnabled,
    isAudioPaused: isAudioPausedByPopup,
    shouldAudioPlay,
    audioState,
    activePopups: Array.from(activePopups),
    
    // Audio controls
    toggleAudio,
    registerAudioRef,
    forceAudioPause,
    forceAudioResume,
    
    // Popup management
    addPopup,
    removePopup,
    
    // Audio component callbacks
    handleAudioStateChange,
    updateAudioState
  };

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  );
}

/**
 * Hook to use audio context
 */
export function useAudioContext() {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudioContext must be used within an AudioContextProvider');
  }
  return context;
}

/**
 * Hook with safe fallback for components that might not have audio context
 */
export function useAudioContextSafe() {
  const context = useContext(AudioContext);
  
  if (!context) {
    // Return safe fallback values
    return {
      isAudioEnabled: true,
      isAudioPaused: false,
      shouldAudioPlay: true,
      audioState: { isPlaying: false, isLoaded: false, currentTime: 0, duration: 0, error: null },
      activePopups: [],
      toggleAudio: () => console.warn('AudioContext not available'),
      registerAudioRef: () => console.warn('AudioContext not available'),
      forceAudioPause: () => console.warn('AudioContext not available'),
      forceAudioResume: () => console.warn('AudioContext not available'),
      addPopup: () => console.warn('AudioContext not available'),
      removePopup: () => console.warn('AudioContext not available'),
      handleAudioStateChange: () => console.warn('AudioContext not available'),
      updateAudioState: () => console.warn('AudioContext not available')
    };
  }
  
  return context;
}
